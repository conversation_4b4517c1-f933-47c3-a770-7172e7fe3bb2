/*
 * DemonCrystal - Advanced Crystal Combat System for Skylandia
 *
 * A demonic crystal combat module that unleashes hell upon your enemies with intelligent
 * multi-crystal placement, adaptive targeting, and emergency escape mechanisms.
 *
 * Features:
 * - Multi-Crystal Warfare: Simultaneous placement at multiple strategic locations
 * - Demonic Auto-Pearl: Emergency escape when death approaches
 * - Intelligent damage calculation and prediction
 * - Advanced rotation and timing systems
 * - Comprehensive safety mechanisms
 */

package dev.journey.Skylandia.modules.automation;

import com.google.common.util.concurrent.AtomicDouble;
import it.unimi.dsi.fastutil.ints.*;
import meteordevelopment.meteorclient.events.entity.EntityAddedEvent;
import meteordevelopment.meteorclient.events.entity.EntityRemovedEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IBox;
import meteordevelopment.meteorclient.mixininterface.IMiningToolItem;
import meteordevelopment.meteorclient.mixininterface.IRaycastContext;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.friends.Friends;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.entity.Target;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.RenderUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockIterator;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.TickRate;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.*;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.*;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.world.RaycastContext;
import org.joml.Vector3d;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

public class DemonCrystal extends Module {

    // === ENUMS ===

    public enum RotationMode {
        None("None"),
        Break("Break Only"),
        Place("Place Only"),
        Both("Both");

        private final String name;
        RotationMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum SwitchMode {
        Disabled("Disabled"),
        Normal("Normal"),
        Silent("Silent"),
        Instant("Instant");

        private final String name;
        SwitchMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum SupportMode {
        Disabled("Disabled"),
        Accurate("Accurate"),
        Fast("Fast"),
        Smart("Smart");

        private final String name;
        SupportMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum MultiCrystalMode {
        Disabled("Disabled"),
        Dual("Dual Crystal"),
        Triple("Triple Crystal"),
        Quad("Quad Crystal"),
        Adaptive("Adaptive");

        private final String name;
        MultiCrystalMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum AutoPearlMode {
        Disabled("Disabled"),
        Health("Health Based"),
        Totems("Totem Based"),
        Combined("Health + Totems"),
        Smart("Smart Escape");

        private final String name;
        AutoPearlMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }

    public enum TargetPriority {
        Closest("Closest"),
        LowestHealth("Lowest Health"),
        HighestDamage("Highest Damage"),
        MostArmor("Most Armor"),
        LeastArmor("Least Armor");

        private final String name;
        TargetPriority(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }
    // === SETTING GROUPS ===
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("Demonic Targeting");
    private final SettingGroup sgPlacement = settings.createGroup("Crystal Placement");
    private final SettingGroup sgMultiCrystal = settings.createGroup("Multi-Crystal Warfare");
    private final SettingGroup sgBreaking = settings.createGroup("Crystal Breaking");
    private final SettingGroup sgAutoPearl = settings.createGroup("Auto-Pearl Escape");
    private final SettingGroup sgSwitching = settings.createGroup("Item Switching");
    private final SettingGroup sgSafety = settings.createGroup("Safety Systems");
    private final SettingGroup sgTiming = settings.createGroup("Timing & Delays");
    private final SettingGroup sgRender = settings.createGroup("Visual Rendering");

    // === GENERAL SETTINGS ===
    private final Setting<Boolean> demonMode = sgGeneral.add(new BoolSetting.Builder()
        .name("demon-mode")
        .description("Unleash the full demonic power - enables all aggressive features and optimizations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnEat = sgGeneral.add(new BoolSetting.Builder()
        .name("pause-on-eat")
        .description("Pause crystal combat while eating gapples or other food items.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnMine = sgGeneral.add(new BoolSetting.Builder()
        .name("pause-on-mine")
        .description("Pause crystal combat while mining blocks.")
        .defaultValue(true)
        .build()
    );

    private final Setting<RotationMode> rotationMode = sgGeneral.add(new EnumSetting.Builder<RotationMode>()
        .name("rotation-mode")
        .description("When to rotate towards crystals for server-side legitimacy.")
        .defaultValue(RotationMode.Both)
        .build()
    );

    private final Setting<Double> rotationSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("rotation-speed")
        .description("Maximum degrees to rotate per tick - lower values look more legit.")
        .defaultValue(180)
        .range(1, 180)
        .sliderMax(180)
        .build()
    );

    // === DEMONIC TARGETING ===
    private final Setting<Double> targetRange = sgTargeting.add(new DoubleSetting.Builder()
        .name("target-range")
        .description("Maximum range to search for enemies to obliterate with crystals.")
        .defaultValue(12)
        .min(1)
        .sliderMax(20)
        .build()
    );

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
        .name("target-priority")
        .description("How to prioritize multiple targets for maximum demonic efficiency.")
        .defaultValue(TargetPriority.LowestHealth)
        .build()
    );

    private final Setting<Boolean> predictMovement = sgTargeting.add(new BoolSetting.Builder()
        .name("predict-movement")
        .description("Predict enemy movement to place crystals where they will be, not where they are.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionTicks = sgTargeting.add(new DoubleSetting.Builder()
        .name("prediction-ticks")
        .description("How many ticks ahead to predict enemy movement.")
        .defaultValue(3)
        .min(1)
        .max(10)
        .sliderMax(10)
        .visible(predictMovement::get)
        .build()
    );

    private final Setting<Boolean> ignoreNaked = sgTargeting.add(new BoolSetting.Builder()
        .name("ignore-naked")
        .description("Ignore players with no armor - they're not worth the crystals.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Set<EntityType<?>>> targetEntities = sgTargeting.add(new EntityTypeListSetting.Builder()
        .name("target-entities")
        .description("Types of entities to target with demonic crystal fury.")
        .onlyAttackable()
        .defaultValue(EntityType.PLAYER, EntityType.WARDEN, EntityType.WITHER)
        .build()
    );

    // === CRYSTAL PLACEMENT ===
    private final Setting<Boolean> enablePlacement = sgPlacement.add(new BoolSetting.Builder()
        .name("enable-placement")
        .description("Enable crystal placement - the foundation of demonic destruction.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> placeRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("place-range")
        .description("Maximum range to place crystals for optimal battlefield control.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> wallsRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("walls-range")
        .description("Range to place crystals when obstructed by walls or obstacles.")
        .defaultValue(3.5)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamagePlace = sgPlacement.add(new DoubleSetting.Builder()
        .name("min-damage-place")
        .description("Minimum damage a crystal must deal to enemies to be worth placing.")
        .defaultValue(6.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> placement112 = sgPlacement.add(new BoolSetting.Builder()
        .name("1.12-placement")
        .description("Use 1.12 crystal placement rules for maximum compatibility.")
        .defaultValue(false)
        .build()
    );

    private final Setting<SupportMode> supportMode = sgPlacement.add(new EnumSetting.Builder<SupportMode>()
        .name("support-mode")
        .description("Automatically place support blocks when no valid crystal positions exist.")
        .defaultValue(SupportMode.Smart)
        .build()
    );

    private final Setting<Boolean> facePlaceMode = sgPlacement.add(new BoolSetting.Builder()
        .name("face-place")
        .description("Enable face-place mode for low-health enemies - show no mercy.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> facePlaceHealth = sgPlacement.add(new DoubleSetting.Builder()
        .name("face-place-health")
        .description("Enemy health threshold to trigger face-place mode.")
        .defaultValue(8.0)
        .min(1)
        .sliderMax(36)
        .build()
    );

    // === MULTI-CRYSTAL WARFARE ===
    private final Setting<MultiCrystalMode> multiCrystalMode = sgMultiCrystal.add(new EnumSetting.Builder<MultiCrystalMode>()
        .name("multi-crystal-mode")
        .description("Deploy multiple crystals simultaneously for overwhelming demonic assault.")
        .defaultValue(MultiCrystalMode.Dual)
        .build()
    );

    private final Setting<Double> multiCrystalRange = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-range")
        .description("Maximum distance between multiple crystal placements.")
        .defaultValue(3.0)
        .min(1)
        .sliderMax(8)
        .build()
    );

    private final Setting<Integer> multiCrystalDelay = sgMultiCrystal.add(new IntSetting.Builder()
        .name("multi-delay")
        .description("Delay in ticks between placing multiple crystals.")
        .defaultValue(1)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> multiCrystalSync = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("synchronized-detonation")
        .description("Synchronize detonation of multiple crystals for maximum devastation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> multiCrystalMinDamage = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-min-damage")
        .description("Minimum combined damage from all crystals to justify multi-placement.")
        .defaultValue(12.0)
        .min(0)
        .sliderMax(50)
        .build()
    );

    // === AUTO-PEARL ESCAPE ===
    private final Setting<AutoPearlMode> autoPearlMode = sgAutoPearl.add(new EnumSetting.Builder<AutoPearlMode>()
        .name("auto-pearl-mode")
        .description("Automatically throw ender pearls to escape when death approaches.")
        .defaultValue(AutoPearlMode.Combined)
        .build()
    );

    private final Setting<Double> pearlHealthThreshold = sgAutoPearl.add(new DoubleSetting.Builder()
        .name("health-threshold")
        .description("Health level (including absorption) to trigger emergency pearl escape.")
        .defaultValue(6.0)
        .min(1)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> pearlTotemThreshold = sgAutoPearl.add(new IntSetting.Builder()
        .name("totem-threshold")
        .description("Number of totems remaining to trigger emergency pearl escape.")
        .defaultValue(1)
        .min(0)
        .sliderMax(5)
        .build()
    );

    private final Setting<Double> pearlDistance = sgAutoPearl.add(new DoubleSetting.Builder()
        .name("pearl-distance")
        .description("Minimum distance to throw pearls for effective escape.")
        .defaultValue(15.0)
        .min(5)
        .sliderMax(30)
        .build()
    );

    private final Setting<Boolean> pearlOnlyWhenTargeted = sgAutoPearl.add(new BoolSetting.Builder()
        .name("pearl-only-when-targeted")
        .description("Only use emergency pearls when actively being targeted by enemies.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> pearlCooldown = sgAutoPearl.add(new IntSetting.Builder()
        .name("pearl-cooldown")
        .description("Cooldown in ticks between pearl throws to prevent spam.")
        .defaultValue(40)
        .min(20)
        .sliderMax(100)
        .build()
    );

    // === CRYSTAL BREAKING ===
    private final Setting<Boolean> enableBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("enable-breaking")
        .description("Enable crystal breaking - destroy enemy crystals and detonate your own.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> breakRange = sgBreaking.add(new DoubleSetting.Builder()
        .name("break-range")
        .description("Maximum range to break crystals with demonic precision.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamageBreak = sgBreaking.add(new DoubleSetting.Builder()
        .name("min-damage-break")
        .description("Minimum damage to enemies required to break a crystal.")
        .defaultValue(4.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> inhibit = sgBreaking.add(new BoolSetting.Builder()
        .name("inhibit")
        .description("Prevent enemies from placing crystals by breaking them instantly.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> breakAttempts = sgBreaking.add(new IntSetting.Builder()
        .name("break-attempts")
        .description("Maximum attempts to break a crystal before giving up.")
        .defaultValue(3)
        .min(1)
        .sliderMax(10)
        .build()
    );

    // === ITEM SWITCHING ===
    private final Setting<SwitchMode> switchMode = sgSwitching.add(new EnumSetting.Builder<SwitchMode>()
        .name("switch-mode")
        .description("How to switch to crystals and tools automatically.")
        .defaultValue(SwitchMode.Normal)
        .build()
    );

    private final Setting<Boolean> antiWeakness = sgSwitching.add(new BoolSetting.Builder()
        .name("anti-weakness")
        .description("Switch to tools when affected by weakness to break crystals effectively.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> noGapSwitch = sgSwitching.add(new BoolSetting.Builder()
        .name("no-gap-switch")
        .description("Don't switch items while holding gapples - prioritize healing.")
        .defaultValue(true)
        .build()
    );

    // === SAFETY SYSTEMS ===
    private final Setting<Boolean> antiSuicide = sgSafety.add(new BoolSetting.Builder()
        .name("anti-suicide")
        .description("Prevent placing/breaking crystals that would kill you - self-preservation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> maxSelfDamage = sgSafety.add(new DoubleSetting.Builder()
        .name("max-self-damage")
        .description("Maximum damage crystals can deal to yourself before being considered unsafe.")
        .defaultValue(8.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> safetyCheck = sgSafety.add(new BoolSetting.Builder()
        .name("safety-check")
        .description("Perform additional safety checks before crystal operations.")
        .defaultValue(true)
        .build()
    );

    // === TIMING & DELAYS ===
    private final Setting<Integer> placeDelay = sgTiming.add(new IntSetting.Builder()
        .name("place-delay")
        .description("Delay in ticks between crystal placements for timing control.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> breakDelay = sgTiming.add(new IntSetting.Builder()
        .name("break-delay")
        .description("Delay in ticks between crystal breaks for optimal timing.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> switchDelay = sgTiming.add(new IntSetting.Builder()
        .name("switch-delay")
        .description("Delay in ticks after switching items before performing actions.")
        .defaultValue(0)
        .min(0)
        .sliderMax(10)
        .build()
    );

    // === VISUAL RENDERING ===
    private final Setting<Boolean> renderPlacement = sgRender.add(new BoolSetting.Builder()
        .name("render-placement")
        .description("Render placement positions for crystal targeting visualization.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> renderBreaking = sgRender.add(new BoolSetting.Builder()
        .name("render-breaking")
        .description("Render crystals being targeted for destruction.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> placementColor = sgRender.add(new ColorSetting.Builder()
        .name("placement-color")
        .description("Color for crystal placement position rendering.")
        .defaultValue(new SettingColor(255, 0, 0, 100))
        .build()
    );

    private final Setting<SettingColor> breakingColor = sgRender.add(new ColorSetting.Builder()
        .name("breaking-color")
        .description("Color for crystal breaking target rendering.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .build()
    );

    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("Rendering mode for crystal position visualization.")
        .defaultValue(ShapeMode.Both)
        .build()
    );

    // === FIELDS ===
    private final List<BlockPos> multiCrystalPositions = new ArrayList<>();
    private final List<EndCrystalEntity> targetCrystals = new ArrayList<>();
    private Entity currentTarget;
    private int pearlCooldownTicks = 0;
    private int placeTicks = 0;
    private int breakTicks = 0;
    private int switchTicks = 0;
    private boolean emergencyPearlUsed = false;

    public DemonCrystal() {
        super(Skylandia.Automation, "demon-crystal",
            "§4DemonCrystal§r - Unleash demonic crystal warfare upon your enemies. " +
            "Features multi-crystal placement, intelligent targeting, emergency pearl escape, " +
            "and advanced safety systems. Show no mercy in crystal combat.");
    }

    @Override
    public void onActivate() {
        info("§4DemonCrystal§r activated! Preparing for crystal warfare...");
        multiCrystalPositions.clear();
        targetCrystals.clear();
        currentTarget = null;
        pearlCooldownTicks = 0;
        placeTicks = 0;
        breakTicks = 0;
        switchTicks = 0;
        emergencyPearlUsed = false;
    }

    @Override
    public void onDeactivate() {
        info("§4DemonCrystal§r deactivated. Crystal warfare ended.");
        multiCrystalPositions.clear();
        targetCrystals.clear();
        currentTarget = null;
    }

    // === EVENT HANDLERS ===
    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Update cooldowns
        if (pearlCooldownTicks > 0) pearlCooldownTicks--;
        if (placeTicks > 0) placeTicks--;
        if (breakTicks > 0) breakTicks--;
        if (switchTicks > 0) switchTicks--;

        // Check for emergency pearl escape
        if (shouldUsePearl()) {
            usePearl();
            return;
        }

        // Pause checks
        if (shouldPause()) {
            info("Paused - eating or mining");
            return;
        }

        // Find target
        findTarget();
        if (currentTarget == null) {
            // info("No valid targets found");
            return;
        }

        info("Target found: " + currentTarget.getName().getString());

        // Check if we have crystals for placement
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (!crystals.found() && enablePlacement.get()) {
            // info("No crystals found in inventory");
            return;
        }

        // Handle crystal operations
        if (enableBreaking.get()) {
            handleBreaking();
        }
        if (enablePlacement.get()) {
            handlePlacement();
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        if (renderPlacement.get()) {
            for (BlockPos pos : multiCrystalPositions) {
                event.renderer.box(pos, placementColor.get(), placementColor.get(), shapeMode.get(), 0);
            }
        }

        if (renderBreaking.get()) {
            for (EndCrystalEntity crystal : targetCrystals) {
                event.renderer.box(crystal.getBoundingBox(), breakingColor.get(), breakingColor.get(), shapeMode.get(), 0);
            }
        }
    }

    // === CORE METHODS ===
    private boolean shouldPause() {
        if (pauseOnEat.get() && mc.player.isUsingItem()) {
            Item item = mc.player.getMainHandStack().getItem();
            if (item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE) {
                return true;
            }
        }

        if (pauseOnMine.get() && mc.interactionManager != null && mc.interactionManager.isBreakingBlock()) {
            return true;
        }

        return false;
    }

    private boolean shouldUsePearl() {
        if (autoPearlMode.get() == AutoPearlMode.Disabled || pearlCooldownTicks > 0) return false;

        boolean healthLow = false;
        boolean totemsLow = false;

        // Check health
        if (autoPearlMode.get() == AutoPearlMode.Health || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
            float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
            healthLow = health <= pearlHealthThreshold.get();
        }

        // Check totems
        if (autoPearlMode.get() == AutoPearlMode.Totems || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
            int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
            totemsLow = totems <= pearlTotemThreshold.get();
        }

        // Check if being targeted
        if (pearlOnlyWhenTargeted.get() && currentTarget == null) return false;

        return switch (autoPearlMode.get()) {
            case Health -> healthLow;
            case Totems -> totemsLow;
            case Combined -> healthLow && totemsLow;
            case Smart -> healthLow || totemsLow;
            default -> false;
        };
    }

    private void usePearl() {
        FindItemResult pearl = InvUtils.find(Items.ENDER_PEARL);
        if (!pearl.found()) return;

        // Switch to pearl
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(pearl.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Calculate throw direction (away from enemies)
        Vec3d throwDirection = calculatePearlDirection();
        if (throwDirection == null) return;

        // Rotate if needed
        if (rotationMode.get() != RotationMode.None) {
            float yaw = (float) Math.toDegrees(Math.atan2(throwDirection.z, throwDirection.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(throwDirection.y));
            Rotations.rotate(yaw, pitch);
        }

        // Throw pearl
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        pearlCooldownTicks = pearlCooldown.get();
        emergencyPearlUsed = true;

        info("Emergency pearl escape activated!");
    }

    private Vec3d calculatePearlDirection() {
        if (currentTarget == null) return new Vec3d(0, 0, 1); // Default direction

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = currentTarget.getPos();
        Vec3d direction = playerPos.subtract(targetPos).normalize();

        // Add some upward angle for better escape
        return new Vec3d(direction.x, Math.max(0.2, direction.y), direction.z).normalize();
    }

    private void findTarget() {
        currentTarget = null;
        double bestScore = Double.MAX_VALUE;

        for (Entity entity : mc.world.getEntities()) {
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > targetRange.get()) continue;

            double score = calculateTargetScore(entity, distance);
            if (score < bestScore) {
                bestScore = score;
                currentTarget = entity;
            }
        }
    }

    private boolean isValidTarget(Entity entity) {
        if (!(entity instanceof LivingEntity living)) return false;
        if (entity == mc.player) return false;
        if (!targetEntities.get().contains(entity.getType())) return false;
        if (entity.isRemoved() || !entity.isAlive()) return false;

        if (entity instanceof PlayerEntity player && Friends.get().isFriend(player)) return false;

        if (ignoreNaked.get() && entity instanceof PlayerEntity player) {
            // Check if player has armor
            boolean hasArmor = false;
            for (ItemStack stack : player.getArmorItems()) {
                if (!stack.isEmpty()) {
                    hasArmor = true;
                    break;
                }
            }
            if (!hasArmor) return false;
        }

        return true;
    }

    private double calculateTargetScore(Entity entity, double distance) {
        return switch (targetPriority.get()) {
            case Closest -> distance;
            case LowestHealth -> entity instanceof LivingEntity living ? living.getHealth() : distance;
            case HighestDamage -> -calculateDamageToTarget(entity); // Negative for highest
            case MostArmor -> entity instanceof PlayerEntity player ? -getArmorValue(player) : distance;
            case LeastArmor -> entity instanceof PlayerEntity player ? getArmorValue(player) : distance;
        };
    }

    private double calculateDamageToTarget(Entity target) {
        // Find closest crystal position to target for damage calculation
        BlockPos targetPos = target.getBlockPos();
        BlockPos crystalPos = targetPos.add(1, 0, 0); // Simplified
        return calculateCrystalDamage(crystalPos, target);
    }

    private double calculateCrystalDamage(BlockPos crystalPos, Entity target) {
        if (target == null) return 0;

        Vec3d crystalVec = Vec3d.ofCenter(crystalPos);
        Vec3d targetVec = target.getPos();

        // Calculate distance
        double distance = crystalVec.distanceTo(targetVec);
        if (distance > 12) return 0; // Max crystal damage range

        // Base damage calculation (simplified)
        double damage = 12 * (1 - (distance / 12));

        // Apply armor reduction if it's a player
        if (target instanceof PlayerEntity player) {
            int armor = getArmorValue(player);
            damage = damage * (1 - (armor * 0.04)); // 4% reduction per armor point
        }

        // Ensure minimum damage
        return Math.max(0, damage);
    }

    private int getArmorValue(PlayerEntity player) {
        int armor = 0;
        for (ItemStack stack : player.getArmorItems()) {
            if (stack.getItem() instanceof ArmorItem) {
                armor += 1; // Simplified armor counting
            }
        }
        return armor;
    }

    private void handlePlacement() {
        if (placeTicks > 0) return;

        multiCrystalPositions.clear();

        if (multiCrystalMode.get() == MultiCrystalMode.Disabled) {
            // Single crystal placement
            BlockPos pos = findBestCrystalPosition();
            if (pos != null) {
                multiCrystalPositions.add(pos);
                info("Found crystal position: " + pos.toString());
            } else {
                info("No valid crystal position found");
            }
        } else {
            // Multi-crystal placement
            findMultiCrystalPositions();
            info("Multi-crystal positions found: " + multiCrystalPositions.size());
        }

        // Place crystals
        for (BlockPos pos : multiCrystalPositions) {
            info("Attempting to place crystal at: " + pos.toString());
            if (placeCrystal(pos)) {
                info("Crystal placed successfully!");
                placeTicks = placeDelay.get();
                if (multiCrystalDelay.get() > 0) {
                    try {
                        Thread.sleep(multiCrystalDelay.get() * 50); // Convert ticks to ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } else {
                info("Failed to place crystal");
            }
        }
    }

    private void handleBreaking() {
        if (breakTicks > 0) return;

        targetCrystals.clear();

        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity crystal)) continue;
            if (mc.player.distanceTo(crystal) > breakRange.get()) continue;

            double damage = calculateDamageToTarget(currentTarget);
            if (damage >= minDamageBreak.get()) {
                targetCrystals.add(crystal);
            }
        }

        // Break crystals
        for (EndCrystalEntity crystal : targetCrystals) {
            if (breakCrystal(crystal)) {
                breakTicks = breakDelay.get();
                break; // Break one at a time unless synchronized
            }
        }
    }

    private BlockPos findBestCrystalPosition() {
        if (currentTarget == null) return null;

        BlockPos bestPos = null;
        double bestDamage = 0;

        // Search around the target for valid crystal positions
        BlockPos targetPos = currentTarget.getBlockPos();

        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = targetPos.add(x, y, z);

                    if (!isValidCrystalPosition(pos)) continue;
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;

                    double damage = calculateCrystalDamage(pos, currentTarget);
                    double selfDamage = calculateCrystalDamage(pos, mc.player);

                    if (damage < minDamagePlace.get()) continue;
                    if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
                    if (selfDamage > maxSelfDamage.get()) continue;

                    if (damage > bestDamage) {
                        bestDamage = damage;
                        bestPos = pos;
                    }
                }
            }
        }

        return bestPos;
    }

    private void findMultiCrystalPositions() {
        // Simplified multi-crystal position finding
        BlockPos center = mc.player.getBlockPos();
        int count = switch (multiCrystalMode.get()) {
            case Dual -> 2;
            case Triple -> 3;
            case Quad -> 4;
            case Adaptive -> calculateAdaptiveCount();
            default -> 1;
        };

        for (int i = 0; i < count && multiCrystalPositions.size() < count; i++) {
            BlockPos pos = center.add(i, 0, 0); // Simplified positioning
            if (isValidCrystalPosition(pos)) {
                multiCrystalPositions.add(pos);
            }
        }
    }

    private int calculateAdaptiveCount() {
        // Adaptive count based on situation
        if (currentTarget != null && mc.player.distanceTo(currentTarget) < 5) {
            return 4; // Close combat - use all crystals
        }
        return 2; // Default to dual
    }

    private boolean isValidCrystalPosition(BlockPos pos) {
        // Check if position is valid for crystal placement
        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;

        // Check base block
        BlockPos basePos = pos.down();
        if (!mc.world.getBlockState(basePos).getBlock().equals(Blocks.OBSIDIAN) &&
            !mc.world.getBlockState(basePos).getBlock().equals(Blocks.BEDROCK)) return false;

        // Check for existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof EndCrystalEntity && entity.getBlockPos().equals(pos)) {
                return false;
            }
        }

        // Check if we can place here (1.12 placement check)
        if (!placement112.get()) {
            // 1.13+ placement - check for entities in the way
            Box box = new Box(pos.getX(), pos.getY(), pos.getZ(), pos.getX() + 1, pos.getY() + 2, pos.getZ() + 1);
            if (!mc.world.getOtherEntities(null, box).isEmpty()) return false;
        }

        return true;
    }

    private boolean placeCrystal(BlockPos pos) {
        FindItemResult crystal = InvUtils.find(Items.END_CRYSTAL);
        if (!crystal.found()) return false;

        // Switch to crystal
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(crystal.slot(), switchMode.get() == SwitchMode.Silent);
            if (switchDelay.get() > 0) {
                switchTicks = switchDelay.get();
                return false; // Wait for switch delay
            }
        }

        // Rotate towards position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d crystalVec = Vec3d.ofCenter(pos);
            Vec3d playerVec = mc.player.getEyePos();
            Vec3d direction = crystalVec.subtract(playerVec).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Place the crystal
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result);
        return true;
    }

    private boolean breakCrystal(EndCrystalEntity crystal) {
        // Simplified crystal breaking - would need full implementation
        if (rotationMode.get() == RotationMode.Break || rotationMode.get() == RotationMode.Both) {
            Vec3d crystalPos = crystal.getPos();
            Vec3d playerPos = mc.player.getEyePos();
            Vec3d direction = crystalPos.subtract(playerPos).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Attack crystal
        mc.interactionManager.attackEntity(mc.player, crystal);
        return true;
    }
}
